### Task:
Analyze the chat history to determine the necessity of generating search queries, in the given language. By default, **prioritize generating 1-3 broad and relevant search queries** unless it is absolutely certain that no additional information is required. The aim is to retrieve comprehensive, updated, and valuable information even with minimal uncertainty. If no search is unequivocally needed, return an empty list.

### Guidelines:
- Respond **EXCLUSIVELY** with a JSON object. Any form of extra commentary, explanation, or additional text is strictly prohibited.
- When generating search queries, respond in the format: { "queries": ["query1", "query2"] }, ensuring each query is distinct, concise, and relevant to the topic.
- Return { "queries": [] } only if it is absolutely, unambiguously, and logically certain that no additional information could possibly be gained from a search. This should be extremely rare. In all other cases, generate 1–3 useful and broad search queries.
- **IMPORTANT: Do NOT generate search queries for FAQ-type questions about system capabilities**, such as:
  - Questions about uploading files or document types
  - Questions about web search capabilities ("can you search the web?")
  - Questions about CUI handling
  - Questions about what the assistant can or cannot do
  - Questions asking for basic information about the system's features
  For these types of questions, return { "queries": [] } as the assistant already has the information to answer them.
- Err on the side of suggesting search queries if there is **any chance** they might provide useful or updated information, EXCEPT for the FAQ-type questions mentioned above.
- Be concise and focused on composing high-quality search queries, avoiding unnecessary elaboration, commentary, or assumptions.
- Today's date is: {{CURRENT_DATE}}.
- Always prioritize providing actionable and broad queries that maximize informational coverage.

### Output:
Strictly return in JSON format:
{
  "queries": ["query1", "query2"]
}

### Chat History:
<chat_history>
{{MESSAGES:END:6}}
</chat_history>
